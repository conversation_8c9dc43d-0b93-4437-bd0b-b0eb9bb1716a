"""
Smart Inventory Management System - Database Configuration
SQLAlchemy database connection and session management
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "sqlite:///./smart_inventory.db"  # Default to SQLite for development
)

# Create SQLAlchemy engine
if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False}  # SQLite specific
    )
else:
    engine = create_engine(DATABASE_URL)

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class
Base = declarative_base()

# Dependency to get database session
def get_db():
    """
    Dependency function to get database session
    Used with FastAPI's Depends() for dependency injection
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Database initialization function
def init_db():
    """
    Initialize database tables
    """
    from app.models import Base
    Base.metadata.create_all(bind=engine)

# Database health check
def check_db_connection():
    """
    Check if database connection is working
    """
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False

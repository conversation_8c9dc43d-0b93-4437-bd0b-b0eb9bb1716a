"""
Smart Inventory Management System - Main Application Entry Point
FastAPI application with CORS middleware and route registration
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.database import engine, Base
from app.routes import inventory_routes, order_routes, auth_routes

# Create database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="Smart Inventory Management API",
    description="API for automated inventory management in small warehouses",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_routes.router, prefix="/api/auth", tags=["authentication"])
app.include_router(inventory_routes.router, prefix="/api/inventory", tags=["inventory"])
app.include_router(order_routes.router, prefix="/api/orders", tags=["orders"])

@app.get("/")
async def root():
    return {"message": "Smart Inventory Management System API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "smart-inventory-api"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
